"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/contact";
exports.ids = ["pages/api/contact"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "nodemailer":
/*!*****************************!*\
  !*** external "nodemailer" ***!
  \*****************************/
/***/ ((module) => {

module.exports = require("nodemailer");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccontact.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccontact.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_contact_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\contact.ts */ \"(api)/./pages/api/contact.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_contact_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_contact_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/contact\",\n        pathname: \"/api/contact\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _pages_api_contact_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmNvbnRhY3QmcHJlZmVycmVkUmVnaW9uPSZhYnNvbHV0ZVBhZ2VQYXRoPS4lMkZwYWdlcyU1Q2FwaSU1Q2NvbnRhY3QudHMmbWlkZGxld2FyZUNvbmZpZ0Jhc2U2ND1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQXNHO0FBQ3ZDO0FBQ0w7QUFDMUQ7QUFDcUQ7QUFDckQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLGtEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLGVBQWUsd0VBQUssQ0FBQyxrREFBUTtBQUNwQztBQUNPLHdCQUF3QixnSEFBbUI7QUFDbEQ7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsWUFBWTtBQUNaLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly93YWxlZWQtYWxtc2h3bHktbmV4dGpzLz9jZDlhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9wYWdlc1xcXFxhcGlcXFxcY29udGFjdC50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2NvbnRhY3RcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9jb250YWN0XCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccontact.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./pages/api/contact.ts":
/*!******************************!*\
  !*** ./pages/api/contact.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nodemailer */ \"nodemailer\");\n/* harmony import */ var nodemailer__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(nodemailer__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            error: \"Method not allowed\"\n        });\n    }\n    const { name, email, subject, message } = req.body;\n    if (!name || !email || !subject || !message) {\n        return res.status(400).json({\n            error: \"Missing required fields\"\n        });\n    }\n    try {\n        const transporter = nodemailer__WEBPACK_IMPORTED_MODULE_0___default().createTransport({\n            service: \"gmail\",\n            auth: {\n                user: process.env.EMAIL_USER,\n                pass: process.env.EMAIL_PASS\n            }\n        });\n        await transporter.sendMail({\n            from: `\"${name}\" <${process.env.EMAIL_USER}>`,\n            to: process.env.EMAIL_TO,\n            replyTo: email,\n            subject: `[رسالة من الموقع] ${subject}`,\n            html: `\n        <div style=\"font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 20px; color: #333;\">\n          <h2 style=\"color: #4A90E2;\">رسالة جديدة من موقعك الشخصي</h2>\n          <hr style=\"border: none; border-top: 1px solid #eee; margin: 20px 0;\" />\n\n          <p><strong>👤 الاسم:</strong> ${name}</p>\n          <p><strong>📧 البريد الإلكتروني:</strong> <a href=\"mailto:${email}\" style=\"color: #4A90E2;\">${email}</a></p>\n          <p><strong>📝 الموضوع:</strong> ${subject}</p>\n\n          <div style=\"margin-top: 20px;\">\n            <p style=\"margin-bottom: 8px;\"><strong>💬 الرسالة:</strong></p>\n            <div style=\"padding: 15px; background-color: #f9f9f9; border-left: 4px solid #4A90E2; line-height: 1.6;\">\n              ${message.replace(/\\n/g, \"<br />\")}\n            </div>\n          </div>\n\n          <hr style=\"border: none; border-top: 1px solid #eee; margin: 30px 0;\" />\n          <footer style=\"font-size: 12px; color: #888;\">\n            تم إرسال هذه الرسالة من نموذج الاتصال في موقعك.\n          </footer>\n        </div>\n      `\n        });\n        return res.status(200).json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Failed to send email:\", error);\n        return res.status(500).json({\n            error: \"Failed to send email\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./pages/api/contact.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Ccontact.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();