import type { NextApiRequest, NextApiResponse } from 'next';
import nodemailer from 'nodemailer';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { name, email, subject, message } = req.body;

  if (!name || !email || !subject || !message) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  try {
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.sendMail({
      from: `"${name}" <${process.env.EMAIL_USER}>`, // بريدك
      to: process.env.EMAIL_TO,
      replyTo: email, // المستخدم هو المرسل الحقيقي
      subject: `[رسالة من الموقع] ${subject}`,
      html: `
        <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 20px; color: #333;">
          <h2 style="color: #4A90E2;">رسالة جديدة من موقعك الشخصي</h2>
          <hr style="border: none; border-top: 1px solid #eee; margin: 20px 0;" />

          <p><strong>👤 الاسم:</strong> ${name}</p>
          <p><strong>📧 البريد الإلكتروني:</strong> <a href="mailto:${email}" style="color: #4A90E2;">${email}</a></p>
          <p><strong>📝 الموضوع:</strong> ${subject}</p>

          <div style="margin-top: 20px;">
            <p style="margin-bottom: 8px;"><strong>💬 الرسالة:</strong></p>
            <div style="padding: 15px; background-color: #f9f9f9; border-left: 4px solid #4A90E2; line-height: 1.6;">
              ${message.replace(/\n/g, '<br />')}
            </div>
          </div>

          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;" />
          <footer style="font-size: 12px; color: #888;">
            تم إرسال هذه الرسالة من نموذج الاتصال في موقعك.
          </footer>
        </div>
      `,
    });

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('Failed to send email:', error);
    return res.status(500).json({ error: 'Failed to send email' });
  }
}
